#!/usr/bin/env python3
"""
YouTube Audio Downloader from InputURL.md
==========================================

This script reads YouTube URLs from an InputURL.md file and downloads audio files
to an [Audio-Only] folder using yt-dlp. It processes all URLs in the markdown file
and creates high-quality audio files.

Features:
- Reads URLs from InputURL.md file (supports external markdown format)
- Downloads highest quality audio using yt-dlp
- Saves files to [Audio-Only] folder
- Uses hardcoded paths if they exist, otherwise prompts for folder selection
- Skips already downloaded files
- Progress tracking and error handling

Requirements:
- yt-dlp: pip install yt-dlp
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import re
import sys
import time
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox

try:
    import yt_dlp
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install yt-dlp")
    sys.exit(1)


# Configuration
HARDCODED_FOLDER_PATH = r"C:\Users\<USER>\Documents\HEW Notes\Video Transcripts MD"
INPUT_URL_FILENAME = "InputURL.md"
AUDIO_FOLDER_NAME = "audio"
DELAY_BETWEEN_DOWNLOADS = 2  # seconds to wait between downloads
DEFAULT_BATCH_SIZE = 10  # default number of URLs to process at once


def extract_urls_from_markdown(file_path):
    """Extract YouTube URLs from InputURL.md file with original line content."""
    urls = []
    if not os.path.exists(file_path):
        print(f"Error: InputURL.md file not found at {file_path}")
        return urls

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                original_line = line  # Keep original line for removal
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # Extract URL from external markdown format: - [title](url)
                if line.startswith('- [') and '](' in line and line.endswith(')'):
                    try:
                        title_end = line.find('](')
                        url = line[title_end + 2:-1]
                        title = line[3:title_end]  # Remove '- [' prefix
                        urls.append({
                            'url': url,
                            'title': title,
                            'line': line_num,
                            'original_line': original_line
                        })
                    except Exception as e:
                        print(f"Warning: Could not parse line {line_num}: {line}")
                        continue

                # Also handle simple markdown format: [title](url)
                elif line.startswith('[') and '](' in line and line.endswith(')'):
                    try:
                        title_end = line.find('](')
                        url = line[title_end + 2:-1]
                        title = line[1:title_end]  # Remove '[' prefix
                        urls.append({
                            'url': url,
                            'title': title,
                            'line': line_num,
                            'original_line': original_line
                        })
                    except Exception as e:
                        print(f"Warning: Could not parse line {line_num}: {line}")
                        continue

                # Handle plain URLs
                elif line.startswith('http'):
                    urls.append({
                        'url': line,
                        'title': 'Unknown Title',
                        'line': line_num,
                        'original_line': original_line
                    })

    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return []

    return urls


def get_folder_path(folder_type="InputURL.md"):
    """Get folder path, use hardcoded path if it exists."""
    if os.path.exists(HARDCODED_FOLDER_PATH):
        return HARDCODED_FOLDER_PATH
    
    # Browse for folder if hardcoded path doesn't exist
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    folder_path = filedialog.askdirectory(
        title=f"Select folder containing {folder_type}",
        initialdir=os.getcwd()
    )
    
    if not folder_path:
        messagebox.showwarning("No Folder Selected", "No folder was selected. Exiting.")
        return None
        
    return folder_path


def create_audio_folder(base_path):
    """Create [Audio-Only] folder if it doesn't exist."""
    audio_folder = os.path.join(base_path, AUDIO_FOLDER_NAME)
    if not os.path.exists(audio_folder):
        try:
            os.makedirs(audio_folder)
            print(f"Created audio folder: {audio_folder}")
        except OSError as e:
            print(f"Error creating audio folder {audio_folder}: {e}")
            return None
    return audio_folder


def sanitize_filename(filename):
    """Sanitize filename for safe file system usage."""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove extra spaces and limit length
    filename = re.sub(r'\s+', ' ', filename).strip()
    if len(filename) > 200:  # Leave room for extension
        filename = filename[:200].strip()
    
    return filename


def get_existing_audio_files(audio_folder):
    """Get list of existing audio files to avoid re-downloading."""
    existing_files = set()
    if os.path.exists(audio_folder):
        for file in os.listdir(audio_folder):
            if file.lower().endswith(('.m4a', '.mp3', '.wav', '.aac', '.ogg')):
                # Remove extension and normalize for comparison
                name_without_ext = os.path.splitext(file)[0].lower()
                existing_files.add(name_without_ext)
    return existing_files


def remove_url_from_file(input_file_path, original_line_to_remove):
    """Remove a specific line from the InputURL.md file."""
    try:
        # Read all lines
        with open(input_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Filter out the line to remove
        updated_lines = [line for line in lines if line != original_line_to_remove]

        # Write back to file
        with open(input_file_path, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)

        return True
    except Exception as e:
        print(f"Error removing URL from file: {e}")
        return False


def get_user_preferences():
    """Get user preferences for batch processing."""
    print("\n" + "=" * 50)
    print("BATCH PROCESSING OPTIONS")
    print("=" * 50)

    # Batch size
    while True:
        try:
            batch_input = input(f"Enter batch size (default {DEFAULT_BATCH_SIZE}, 0 for all): ").strip()
            if not batch_input:
                batch_size = DEFAULT_BATCH_SIZE
                break
            batch_size = int(batch_input)
            if batch_size < 0:
                print("Batch size must be 0 or positive")
                continue
            break
        except ValueError:
            print("Please enter a valid number")

    # Remove URLs option
    while True:
        remove_input = input("Remove URLs from InputURL.md after successful download? (y/n, default y): ").strip().lower()
        if not remove_input or remove_input in ['y', 'yes']:
            remove_urls = True
            break
        elif remove_input in ['n', 'no']:
            remove_urls = False
            break
        else:
            print("Please enter 'y' or 'n'")

    # Delay between downloads
    while True:
        try:
            delay_input = input(f"Delay between downloads in seconds (default {DELAY_BETWEEN_DOWNLOADS}): ").strip()
            if not delay_input:
                delay = DELAY_BETWEEN_DOWNLOADS
                break
            delay = float(delay_input)
            if delay < 0:
                print("Delay must be 0 or positive")
                continue
            break
        except ValueError:
            print("Please enter a valid number")

    return batch_size, remove_urls, delay


def download_youtube_audio(url, title, audio_folder, existing_files, delay=0):
    """Download audio from YouTube URL using yt-dlp."""

    # Check if file already exists
    sanitized_title = sanitize_filename(title).lower()
    if sanitized_title in existing_files:
        print(f"⏭️  Skipping (already exists): {title}")
        return True

    ydl_opts = {
        'format': 'm4a/bestaudio/best',  # Request best audio, prefer m4a container
        'postprocessors': [{
            'key': 'FFmpegExtractAudio',  # Use FFmpeg to extract audio
            'preferredcodec': 'm4a',      # Preferred audio codec
        }],
        'outtmpl': os.path.join(audio_folder, '%(title)s.%(ext)s'),  # Output path template
        'noplaylist': True,  # Download only single video, not playlist
        'quiet': False,  # Set to True to reduce output
        'no_warnings': False,
        'retries': 3,  # Retry failed downloads
        'fragment_retries': 3,  # Retry failed fragments
    }

    print(f"🎵 Downloading: {title}")
    print(f"   URL: {url}")

    # Add delay before download to avoid rate limiting
    if delay > 0:
        print(f"   ⏳ Waiting {delay} seconds...")
        time.sleep(delay)

    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
        try:
            ydl.download([url])
            print(f"✅ Successfully downloaded: {title}")
            return True
        except yt_dlp.utils.DownloadError as e:
            print(f"❌ Download error for '{title}': {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error for '{title}': {e}")
            return False


def main():
    """Main function to process URLs and download audio files."""
    print("YouTube Audio Downloader from InputURL.md")
    print("=" * 50)
    
    # Get folder containing InputURL.md
    base_folder = get_folder_path("InputURL.md")
    if not base_folder:
        return
    
    # Check for InputURL.md file
    input_file_path = os.path.join(base_folder, INPUT_URL_FILENAME)
    if not os.path.exists(input_file_path):
        print(f"Error: {INPUT_URL_FILENAME} not found in {base_folder}")
        return
    
    # Extract URLs from markdown file
    print(f"Reading URLs from: {input_file_path}")
    urls_data = extract_urls_from_markdown(input_file_path)
    
    if not urls_data:
        print("No URLs found in the InputURL.md file")
        return
    
    print(f"Found {len(urls_data)} URL(s) to process:")
    for i, data in enumerate(urls_data, 1):
        print(f"  {i}. {data['title'][:60]}{'...' if len(data['title']) > 60 else ''}")

    # Get user preferences for batch processing
    batch_size, remove_urls, delay = get_user_preferences()

    # Determine URLs to process
    if batch_size == 0:
        urls_to_process = urls_data
        print(f"\nProcessing all {len(urls_to_process)} URLs")
    else:
        urls_to_process = urls_data[:batch_size]
        print(f"\nProcessing first {len(urls_to_process)} URLs (batch size: {batch_size})")
        if len(urls_data) > batch_size:
            print(f"Remaining {len(urls_data) - batch_size} URLs will be processed in future runs")

    # Create audio folder
    audio_folder = create_audio_folder(base_folder)
    if not audio_folder:
        return

    print(f"\nAudio files will be saved to: {audio_folder}")
    print(f"Remove URLs after download: {'Yes' if remove_urls else 'No'}")
    print(f"Delay between downloads: {delay} seconds")

    # Get existing audio files to avoid re-downloading
    existing_files = get_existing_audio_files(audio_folder)
    if existing_files:
        print(f"Found {len(existing_files)} existing audio files")

    # Process each URL
    print(f"\nStarting download process...")
    print("-" * 50)
    
    successful_downloads = 0
    failed_downloads = 0
    skipped_downloads = 0
    removed_urls = 0

    for i, data in enumerate(urls_to_process, 1):
        print(f"\n[{i}/{len(urls_to_process)}] Processing:")

        # Check if already exists before attempting download
        sanitized_title = sanitize_filename(data['title']).lower()
        if sanitized_title in existing_files:
            print(f"⏭️  Skipping (already exists): {data['title']}")
            skipped_downloads += 1

            # Remove URL from file if requested (even for skipped files)
            if remove_urls:
                if remove_url_from_file(input_file_path, data['original_line']):
                    removed_urls += 1
                    print(f"   🗑️  Removed URL from InputURL.md")
            continue

        success = download_youtube_audio(
            data['url'],
            data['title'],
            audio_folder,
            existing_files,
            delay if i > 1 else 0  # No delay for first download
        )

        if success:
            successful_downloads += 1
            # Add to existing files set to avoid re-downloading in same session
            existing_files.add(sanitized_title)

            # Remove URL from file if requested and download was successful
            if remove_urls:
                if remove_url_from_file(input_file_path, data['original_line']):
                    removed_urls += 1
                    print(f"   🗑️  Removed URL from InputURL.md")
                else:
                    print(f"   ⚠️  Failed to remove URL from InputURL.md")
        else:
            failed_downloads += 1
    
    # Summary
    print("\n" + "=" * 50)
    print("DOWNLOAD SUMMARY")
    print("=" * 50)
    print(f"✅ Successful downloads: {successful_downloads}")
    print(f"⏭️  Skipped (already exist): {skipped_downloads}")
    print(f"❌ Failed downloads: {failed_downloads}")
    if remove_urls:
        print(f"🗑️  URLs removed from InputURL.md: {removed_urls}")
    print(f"📁 Audio folder: {audio_folder}")

    # Show remaining URLs info
    if batch_size > 0 and len(urls_data) > batch_size:
        remaining = len(urls_data) - batch_size
        print(f"\n📋 {remaining} URLs remaining in InputURL.md for next batch")
    elif remove_urls and removed_urls > 0:
        remaining_in_file = len(urls_data) - removed_urls
        print(f"\n📋 Approximately {remaining_in_file} URLs remaining in InputURL.md")

    if failed_downloads > 0:
        print(f"\n⚠️  {failed_downloads} downloads failed. Check the URLs and try again.")
        print("   Failed URLs were not removed from InputURL.md")

    # Rate limiting warning
    if successful_downloads > 5:
        print(f"\n💡 TIP: If you encounter rate limiting, increase the delay between downloads")
        print("   or process smaller batches to avoid being blocked by YouTube")

    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
