#!/usr/bin/env python3
"""
Audio Metadata Checker
======================

This script checks and displays metadata from audio files (.m4a, .mp3, .wav, .aac, .ogg).
It can process individual files or entire directories and provides detailed metadata information.

Features:
- Check metadata for individual audio files
- Batch check all audio files in a directory
- Display comprehensive metadata information
- Export metadata to JSON or CSV format
- Verify specific metadata fields (title, artist, date, URL)
- Support for multiple audio formats

Requirements:
- ffprobe (part of FFmpeg): Required for reading metadata
- mutagen: pip install mutagen (optional, for custom URL metadata in M4A files)
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import sys
import json
import csv
import subprocess
from pathlib import Path
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime

try:
    from mutagen.mp4 import MP4
    MUTAGEN_AVAILABLE = True
except ImportError:
    MUTAGEN_AVAILABLE = False


def check_ffprobe_availability():
    """Check if FFprobe is available on the system."""
    try:
        result = subprocess.run(['ffprobe', '-version'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        return False


def get_audio_metadata(file_path):
    """Extract metadata from audio file using FFprobe."""
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            file_path
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            metadata_info = json.loads(result.stdout)
            format_info = metadata_info.get('format', {})
            streams = metadata_info.get('streams', [])
            
            # Get basic file info
            file_info = {
                'filename': os.path.basename(file_path),
                'filepath': file_path,
                'filesize': format_info.get('size', 'Unknown'),
                'duration': format_info.get('duration', 'Unknown'),
                'bitrate': format_info.get('bit_rate', 'Unknown'),
                'format_name': format_info.get('format_name', 'Unknown'),
            }
            
            # Get audio stream info
            audio_stream = next((s for s in streams if s.get('codec_type') == 'audio'), {})
            if audio_stream:
                file_info.update({
                    'codec': audio_stream.get('codec_name', 'Unknown'),
                    'sample_rate': audio_stream.get('sample_rate', 'Unknown'),
                    'channels': audio_stream.get('channels', 'Unknown'),
                })
            
            # Get metadata tags
            tags = format_info.get('tags', {})
            
            # Normalize tag keys (FFmpeg can return different case variations)
            normalized_tags = {}
            for key, value in tags.items():
                normalized_tags[key.lower()] = value
            
            metadata = {
                'title': normalized_tags.get('title', 'Not found'),
                'artist': normalized_tags.get('artist', 'Not found'),
                'date': normalized_tags.get('date', 'Not found'),
                'url': normalized_tags.get('url', 'Not found'),
                'comment': normalized_tags.get('comment', 'Not found'),
                'album': normalized_tags.get('album', 'Not found'),
                'genre': normalized_tags.get('genre', 'Not found'),
            }

            # Try to get URL from mutagen if available and file is M4A
            custom_url = 'Not found'
            if MUTAGEN_AVAILABLE and file_path.lower().endswith('.m4a'):
                try:
                    audio_file = MP4(file_path)
                    url_field = audio_file.get('----:com.apple.iTunes:URL')
                    if url_field:
                        custom_url = url_field[0].decode('utf-8') if url_field[0] else 'Not found'
                except Exception:
                    pass  # Ignore mutagen errors

            metadata['custom_url'] = custom_url
            
            return {
                'success': True,
                'file_info': file_info,
                'metadata': metadata,
                'all_tags': normalized_tags
            }
        else:
            return {
                'success': False,
                'error': f"FFprobe error: {result.stderr}",
                'filepath': file_path
            }
            
    except json.JSONDecodeError as e:
        return {
            'success': False,
            'error': f"JSON parsing error: {e}",
            'filepath': file_path
        }
    except Exception as e:
        return {
            'success': False,
            'error': f"Unexpected error: {e}",
            'filepath': file_path
        }


def format_duration(duration_str):
    """Format duration from seconds to readable format."""
    try:
        duration = float(duration_str)
        hours = int(duration // 3600)
        minutes = int((duration % 3600) // 60)
        seconds = int(duration % 60)
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    except (ValueError, TypeError):
        return str(duration_str)


def format_filesize(size_str):
    """Format file size to readable format."""
    try:
        size = int(size_str)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    except (ValueError, TypeError):
        return str(size_str)


def print_metadata_info(result):
    """Print formatted metadata information."""
    if not result['success']:
        print(f"❌ Error processing {result['filepath']}: {result['error']}")
        return
    
    file_info = result['file_info']
    metadata = result['metadata']
    
    print(f"\n📁 File: {file_info['filename']}")
    print(f"   Path: {file_info['filepath']}")
    print(f"   Size: {format_filesize(file_info['filesize'])}")
    print(f"   Duration: {format_duration(file_info['duration'])}")
    print(f"   Format: {file_info['format_name']}")
    print(f"   Codec: {file_info['codec']}")
    print(f"   Bitrate: {file_info['bitrate']} bps" if file_info['bitrate'] != 'Unknown' else f"   Bitrate: Unknown")
    print(f"   Sample Rate: {file_info['sample_rate']} Hz" if file_info['sample_rate'] != 'Unknown' else f"   Sample Rate: Unknown")
    print(f"   Channels: {file_info['channels']}")
    
    print(f"\n📋 Metadata:")
    print(f"   Title: {metadata['title']}")
    print(f"   Artist: {metadata['artist']}")
    print(f"   Date: {metadata['date']}")
    print(f"   URL (standard): {metadata['url']}")
    print(f"   URL (custom): {metadata['custom_url']}")
    print(f"   Comment: {metadata['comment']}")
    print(f"   Album: {metadata['album']}")
    print(f"   Genre: {metadata['genre']}")

    # Check for missing critical metadata
    critical_fields = ['title', 'artist', 'date']
    missing_fields = [field for field in critical_fields if metadata[field] == 'Not found']

    # Check URL fields - either standard or custom should be present
    has_url = metadata['url'] != 'Not found' or metadata['custom_url'] != 'Not found'

    if missing_fields:
        print(f"   ⚠️  Missing critical metadata: {', '.join(missing_fields)}")
    elif not has_url:
        print(f"   ⚠️  Missing URL metadata (neither standard nor custom URL found)")
    else:
        print(f"   ✅ All critical metadata fields present")


def get_audio_files(directory):
    """Get list of audio files in directory."""
    audio_extensions = {'.m4a', '.mp3', '.wav', '.aac', '.ogg', '.flac', '.wma'}
    audio_files = []
    
    for file_path in Path(directory).rglob('*'):
        if file_path.is_file() and file_path.suffix.lower() in audio_extensions:
            audio_files.append(str(file_path))
    
    return sorted(audio_files)


def export_metadata_to_json(results, output_file):
    """Export metadata results to JSON file."""
    try:
        export_data = {
            'export_date': datetime.now().isoformat(),
            'total_files': len(results),
            'successful': len([r for r in results if r['success']]),
            'failed': len([r for r in results if not r['success']]),
            'files': results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Metadata exported to: {output_file}")
        return True
    except Exception as e:
        print(f"❌ Error exporting to JSON: {e}")
        return False


def export_metadata_to_csv(results, output_file):
    """Export metadata results to CSV file."""
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # Write header
            writer.writerow([
                'Filename', 'Filepath', 'Success', 'Error', 'Filesize', 'Duration',
                'Format', 'Codec', 'Bitrate', 'Sample_Rate', 'Channels',
                'Title', 'Artist', 'Date', 'URL_Standard', 'URL_Custom', 'Comment', 'Album', 'Genre'
            ])
            
            # Write data
            for result in results:
                if result['success']:
                    file_info = result['file_info']
                    metadata = result['metadata']
                    writer.writerow([
                        file_info['filename'], file_info['filepath'], 'Yes', '',
                        file_info['filesize'], file_info['duration'], file_info['format_name'],
                        file_info['codec'], file_info['bitrate'], file_info['sample_rate'],
                        file_info['channels'], metadata['title'], metadata['artist'],
                        metadata['date'], metadata['url'], metadata['custom_url'], metadata['comment'],
                        metadata['album'], metadata['genre']
                    ])
                else:
                    writer.writerow([
                        os.path.basename(result['filepath']), result['filepath'], 'No',
                        result['error'], '', '', '', '', '', '', '', '', '', '', '', '', '', '', ''
                    ])
        
        print(f"✅ Metadata exported to: {output_file}")
        return True
    except Exception as e:
        print(f"❌ Error exporting to CSV: {e}")
        return False


def select_file_or_directory():
    """Let user select a file or directory to check."""
    root = tk.Tk()
    root.withdraw()
    
    choice = messagebox.askyesnocancel(
        "Select Input",
        "Select input type:\n\nYes = Single audio file\nNo = Directory (all audio files)\nCancel = Exit"
    )
    
    if choice is None:  # Cancel
        return None, None
    elif choice:  # Yes - single file
        file_path = filedialog.askopenfilename(
            title="Select audio file to check",
            filetypes=[
                ("Audio files", "*.m4a *.mp3 *.wav *.aac *.ogg *.flac *.wma"),
                ("All files", "*.*")
            ]
        )
        return file_path if file_path else None, 'file'
    else:  # No - directory
        dir_path = filedialog.askdirectory(
            title="Select directory containing audio files"
        )
        return dir_path if dir_path else None, 'directory'


def main():
    """Main function to check audio metadata."""
    print("Audio Metadata Checker")
    print("=" * 50)
    
    # Check if FFprobe is available
    if not check_ffprobe_availability():
        print("❌ FFprobe not found!")
        print("   FFprobe is required to read audio metadata.")
        print("   Please install FFmpeg (which includes FFprobe):")
        print("   - Windows: Download from https://ffmpeg.org/download.html")
        print("   - macOS: brew install ffmpeg")
        print("   - Linux: sudo apt install ffmpeg (Ubuntu/Debian)")
        input("\nPress Enter to exit...")
        return
    
    # Get input path and type
    input_path, input_type = select_file_or_directory()
    if not input_path:
        print("No input selected. Exiting.")
        return
    
    # Process files
    results = []
    
    if input_type == 'file':
        print(f"\n🔍 Checking metadata for: {input_path}")
        result = get_audio_metadata(input_path)
        results.append(result)
        print_metadata_info(result)
    
    elif input_type == 'directory':
        audio_files = get_audio_files(input_path)
        if not audio_files:
            print(f"No audio files found in: {input_path}")
            return
        
        print(f"\n🔍 Found {len(audio_files)} audio files in: {input_path}")
        print("Processing files...")
        
        for i, file_path in enumerate(audio_files, 1):
            print(f"\n[{i}/{len(audio_files)}] Processing: {os.path.basename(file_path)}")
            result = get_audio_metadata(file_path)
            results.append(result)
            print_metadata_info(result)
    
    # Summary
    successful = len([r for r in results if r['success']])
    failed = len([r for r in results if not r['success']])
    
    print(f"\n" + "=" * 50)
    print("METADATA CHECK SUMMARY")
    print("=" * 50)
    print(f"✅ Successfully processed: {successful}")
    print(f"❌ Failed to process: {failed}")
    print(f"📁 Total files: {len(results)}")
    
    # Export options
    if results and successful > 0:
        print(f"\n📤 Export options:")
        export_choice = input("Export results? (j=JSON, c=CSV, n=No): ").strip().lower()
        
        if export_choice in ['j', 'json']:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"metadata_check_{timestamp}.json"
            export_metadata_to_json(results, output_file)
        elif export_choice in ['c', 'csv']:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"metadata_check_{timestamp}.csv"
            export_metadata_to_csv(results, output_file)
    
    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
