#!/usr/bin/env python3
"""
Test script to verify the YouTube URL extraction works correctly
"""

import os
import sys
from bs4 import BeautifulSoup
import re

def extract_video_id_from_url(url):
    """Extract YouTube video ID from various URL formats."""
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/)([a-zA-Z0-9_-]{11})',
        r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
        r'youtube\.com/v/([a-zA-Z0-9_-]{11})'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None

def normalize_youtube_url(url):
    """Convert YouTube URL to standard format."""
    video_id = extract_video_id_from_url(url)
    if video_id:
        return f"https://www.youtube.com/watch?v={video_id}"
    return url

def extract_youtube_links_from_html(html_content):
    """Extract YouTube video links and titles from HTML content."""
    soup = BeautifulSoup(html_content, 'html.parser')
    video_data = []
    
    # Look specifically for playlist video renderers (the actual playlist items)
    playlist_videos = soup.find_all('ytd-playlist-video-renderer')
    
    for playlist_video in playlist_videos:
        # Find the video title link within the playlist video renderer
        title_link = playlist_video.find('a', {'id': 'video-title'})
        if title_link and title_link.get('href'):
            href = title_link.get('href')
            title = title_link.get_text(strip=True) or title_link.get('title', 'Untitled')
            
            # Convert relative URLs to absolute
            if href.startswith('/watch?'):
                full_url = f"https://www.youtube.com{href}"
            elif href.startswith('http'):
                full_url = href
            else:
                continue
                
            # Extract video ID and normalize URL
            video_id = extract_video_id_from_url(full_url)
            if video_id:
                normalized_url = normalize_youtube_url(full_url)
                video_data.append({
                    'url': normalized_url,
                    'title': title,
                    'video_id': video_id
                })
    
    return video_data

def main():
    """Test the extraction function."""
    html_file = "youtube_playlist.html"
    
    if not os.path.exists(html_file):
        print(f"Error: {html_file} not found")
        return
    
    print("Testing YouTube URL extraction...")
    print("=" * 50)
    
    # Read HTML file
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    # Extract videos
    video_data = extract_youtube_links_from_html(html_content)
    
    print(f"Found {len(video_data)} videos:")
    print()
    
    for i, video in enumerate(video_data, 1):
        print(f"{i:2d}. {video['title']}")
        print(f"    {video['url']}")
        print()
    
    # Write to test output file
    output_file = "InputURL-test-fixed.txt"
    with open(output_file, 'w', encoding='utf-8') as f:
        for video in video_data:
            f.write(f"- [{video['title']}]({video['url']})\n")
    
    print(f"Results written to {output_file}")

if __name__ == "__main__":
    main()
