#!/usr/bin/env python3
"""
Audio File Transcriber for [Audio-Only] Folder
===============================================

This script transcribes all audio files found in the [Audio-Only] folder using
OpenAI's Whisper model and saves the transcripts to a [Transcript] folder.

Features:
- Processes all audio files in [Audio-Only] folder
- Uses hardcoded paths if they exist, otherwise prompts for folder selection
- Creates transcripts with YAML front matter
- Skips already transcribed files
- Progress tracking and error handling
- Supports multiple audio formats (m4a, mp3, wav, aac, ogg)

Requirements:
- openai-whisper: pip install openai-whisper
- tkinter (usually comes with Python)

Author: Generated for YouTube Transcript Manager
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional, List
import tkinter as tk
from tkinter import filedialog, messagebox

try:
    import whisper
except ImportError as e:
    print(f"Missing required package: {e}")
    print("Please install required packages:")
    print("pip install openai-whisper")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

# Configuration
HARDCODED_FOLDER_PATH = r"C:\Users\<USER>\Documents\HEW Notes\Video Transcripts MD"
AUDIO_FOLDER_NAME = "audio"
TRANSCRIPT_FOLDER_NAME = "transcript"
SUPPORTED_AUDIO_EXTENSIONS = {'.m4a', '.mp3', '.wav', '.aac', '.ogg', '.flac', '.wma'}

# Transcription settings (matching youtube_transcript_manager.py)
CHUNK_SECONDS = 30
WHISPER_MODEL = "large"  # Use large model for better accuracy
PADDING_SECONDS = 5.0  # Padding for each chunk


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for filesystem compatibility."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename[:200].strip()


def format_time(seconds: float) -> str:
    """Convert seconds to HH:MM:SS format."""
    try:
        if seconds < 0:
            seconds = 0

        integer_seconds = int(seconds)
        hours, remainder = divmod(integer_seconds, 3600)
        minutes, secs = divmod(remainder, 60)
        return f'{hours:02}:{minutes:02}:{secs:02}'
    except:
        return '00:00:00'


def create_timestamp_url(base_url: str, seconds: float) -> str:
    """Create a timestamped URL for the video (placeholder for audio files)."""
    try:
        timestamp = int(seconds)
        # For audio files, we'll just return a placeholder URL
        return f"#t={timestamp}"
    except:
        return "#"


def get_folder_path(folder_type="folder"):
    """Get folder path, use hardcoded path if it exists."""
    if os.path.exists(HARDCODED_FOLDER_PATH):
        return HARDCODED_FOLDER_PATH
    
    # Browse for folder if hardcoded path doesn't exist
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    folder_path = filedialog.askdirectory(
        title=f"Select base folder containing {folder_type}",
        initialdir=os.getcwd()
    )
    
    if not folder_path:
        messagebox.showwarning("No Folder Selected", "No folder was selected. Exiting.")
        return None
        
    return folder_path


def find_audio_files(audio_folder: Path) -> List[Path]:
    """Find all supported audio files in the audio folder."""
    audio_files = []
    
    if not audio_folder.exists():
        print(f"Error: Audio folder does not exist: {audio_folder}")
        return audio_files
    
    for file_path in audio_folder.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_AUDIO_EXTENSIONS:
            audio_files.append(file_path)
    
    return sorted(audio_files)


def create_transcript_folder(base_path: Path) -> Optional[Path]:
    """Create [Transcript] folder if it doesn't exist."""
    transcript_folder = base_path / TRANSCRIPT_FOLDER_NAME
    if not transcript_folder.exists():
        try:
            transcript_folder.mkdir(parents=True, exist_ok=True)
            print(f"Created transcript folder: {transcript_folder}")
        except OSError as e:
            print(f"Error creating transcript folder {transcript_folder}: {e}")
            return None
    return transcript_folder


def get_existing_transcripts(transcript_folder: Path) -> set:
    """Get set of existing transcript files to avoid re-transcribing."""
    existing_transcripts = set()
    
    if transcript_folder.exists():
        for file_path in transcript_folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() == '.md':
                # Remove extension and normalize for comparison
                name_without_ext = file_path.stem.lower()
                existing_transcripts.add(name_without_ext)
    
    return existing_transcripts


def transcribe_audio_file(audio_path: Path, transcript_folder: Path, model, existing_transcripts: set) -> bool:
    """Transcribe a single audio file using Whisper with chunking approach from youtube_transcript_manager.py."""

    # Check if transcript already exists
    audio_name = sanitize_filename(audio_path.stem).lower()
    if audio_name in existing_transcripts:
        print(f"⏭️  Skipping (transcript exists): {audio_path.name}")
        return True

    print(f"🎵 Transcribing: {audio_path.name}")

    try:
        # Check file exists and has content
        if not audio_path.exists():
            print(f"❌ Audio file does not exist: {audio_path}")
            return False

        file_size = audio_path.stat().st_size
        if file_size == 0:
            print(f"❌ Audio file is empty: {audio_path}")
            return False

        print(f"   File size: {file_size:,} bytes")

        # Transcribe using Whisper
        print("   🔄 Starting Whisper transcription...")
        result = model.transcribe(str(audio_path))

        # Extract results
        segments = result.get('segments', [])
        text = result.get('text', '').strip()

        print(f"   ✅ Transcription completed - {len(segments)} segments, {len(text)} characters")

        # Create transcript file
        safe_title = sanitize_filename(audio_path.stem)
        transcript_path = transcript_folder / f"{safe_title}.md"

        # Create metadata dictionary
        metadata = {
            'title': audio_path.stem,
            'source_file': audio_path.name,
            'url': f"file://{audio_path.absolute()}"  # Local file URL
        }

        print(f"   💾 Writing transcript to: {transcript_path.name}")

        with open(transcript_path, 'w', encoding='utf-8') as f:
            # Write YAML front matter (matching youtube_transcript_manager.py format)
            f.write("---\n")
            f.write(f"title: {metadata['title']}\n")
            f.write(f"author: Local Audio File\n")
            f.write(f"source_file: {metadata['source_file']}\n")
            f.write(f"url: {metadata['url']}\n")
            f.write("---\n\n")

            if not segments:
                # No speech detected - create a placeholder entry
                print("   ⚠️  No speech segments detected, creating placeholder entry")
                f.write("- [00:00:00](#) - [No Content] - No speech detected\n\n")
            else:
                print(f"   📝 Processing {len(segments)} speech segments into {CHUNK_SECONDS}-second chunks")

                # Determine total duration for chunking (matching youtube_transcript_manager.py)
                max_segment_end_time = 0
                if segments:
                    valid_end_times = [s.get('end') for s in segments if s.get('end') is not None]
                    if valid_end_times:
                        max_segment_end_time = max(valid_end_times)
                    else:
                        valid_start_times = [s.get('start') for s in segments if s.get('start') is not None]
                        if valid_start_times:
                            max_segment_end_time = max(valid_start_times)

                # Ensure we have at least one chunk
                if max_segment_end_time == 0:
                    max_segment_end_time = CHUNK_SECONDS

                num_total_chunks = (int(max_segment_end_time) // CHUNK_SECONDS) + 1
                print(f"   📊 Creating {num_total_chunks} chunks of {CHUNK_SECONDS} seconds each (with {PADDING_SECONDS}s padding)")

                for i in range(num_total_chunks):
                    chunk_start_seconds = float(i * CHUNK_SECONDS)
                    chunk_end_seconds = float((i + 1) * CHUNK_SECONDS)

                    # Define extended window for transcription (5 seconds padding)
                    extended_start = max(0, chunk_start_seconds - PADDING_SECONDS)
                    extended_end = chunk_end_seconds + PADDING_SECONDS

                    current_chunk_texts = []

                    for segment in segments:
                        seg_start = segment.get('start')
                        seg_end = segment.get('end')
                        text_content = segment.get('text', '').strip()

                        if seg_start is not None and seg_end is not None and text_content:
                            # Include segments that overlap with the extended window
                            if ((extended_start <= seg_start < extended_end) or
                                (extended_start <= seg_end < extended_end) or
                                (seg_start <= extended_start and seg_end >= extended_end)):
                                current_chunk_texts.append(text_content)

                    full_text_for_chunk = " ".join(current_chunk_texts).strip()

                    if full_text_for_chunk:  # Only write entries with content
                        # Format timestamp
                        timestamp = format_time(chunk_start_seconds)
                        timestamp_url = create_timestamp_url(metadata['url'], chunk_start_seconds)

                        # Use default title - titles will be generated by separate script
                        chunk_title = "Untitled Segment"

                        # Write markdown entry (matching youtube_transcript_manager.py format)
                        f.write(f"- [{timestamp}]({timestamp_url}) - [{chunk_title}] - {full_text_for_chunk}\n\n")

        print(f"   ✅ Transcript created successfully: {transcript_path.name}")
        return True

    except Exception as e:
        print(f"❌ Transcription error for {audio_path.name}: {e}")
        logging.error(f"Transcription error for {audio_path}: {e}")
        return False


def main():
    """Main function to transcribe all audio files."""
    print("Audio File Transcriber for [Audio-Only] Folder")
    print("=" * 50)
    
    # Get base folder
    base_folder = get_folder_path("[Audio-Only] and [Transcript] folders")
    if not base_folder:
        return
    
    base_path = Path(base_folder)
    audio_folder = base_path / AUDIO_FOLDER_NAME
    
    # Check if audio folder exists
    if not audio_folder.exists():
        print(f"Error: {AUDIO_FOLDER_NAME} folder not found in {base_folder}")
        print(f"Expected path: {audio_folder}")
        return
    
    # Find audio files
    print(f"Scanning for audio files in: {audio_folder}")
    audio_files = find_audio_files(audio_folder)
    
    if not audio_files:
        print(f"No audio files found in {audio_folder}")
        print(f"Supported formats: {', '.join(SUPPORTED_AUDIO_EXTENSIONS)}")
        return
    
    print(f"Found {len(audio_files)} audio file(s):")
    for i, audio_file in enumerate(audio_files, 1):
        file_size = audio_file.stat().st_size
        print(f"  {i}. {audio_file.name} ({file_size:,} bytes)")
    
    # Create transcript folder
    transcript_folder = create_transcript_folder(base_path)
    if not transcript_folder:
        return
    
    print(f"\nTranscripts will be saved to: {transcript_folder}")
    
    # Get existing transcripts
    existing_transcripts = get_existing_transcripts(transcript_folder)
    if existing_transcripts:
        print(f"Found {len(existing_transcripts)} existing transcript(s)")
    
    # Load Whisper model
    print(f"\n🔄 Loading Whisper model: {WHISPER_MODEL}")
    print("   ⏳ This may take some time for larger models...")
    try:
        model = whisper.load_model(WHISPER_MODEL)
        print("✅ Whisper model loaded successfully")
    except Exception as e:
        print(f"❌ Error loading Whisper model: {e}")
        print("   💡 Try using a smaller model like 'base' if you encounter memory issues")
        return
    
    # Process each audio file
    print(f"\nStarting transcription process...")
    print("-" * 50)
    
    successful_transcriptions = 0
    failed_transcriptions = 0
    skipped_transcriptions = 0
    
    for i, audio_file in enumerate(audio_files, 1):
        print(f"\n[{i}/{len(audio_files)}] Processing:")
        
        success = transcribe_audio_file(
            audio_file, 
            transcript_folder, 
            model, 
            existing_transcripts
        )
        
        if success:
            if sanitize_filename(audio_file.stem).lower() in existing_transcripts:
                skipped_transcriptions += 1
            else:
                successful_transcriptions += 1
                # Add to existing set to avoid re-processing in same session
                existing_transcripts.add(sanitize_filename(audio_file.stem).lower())
        else:
            failed_transcriptions += 1
    
    # Summary
    print("\n" + "=" * 50)
    print("TRANSCRIPTION SUMMARY")
    print("=" * 50)
    print(f"✅ Successful transcriptions: {successful_transcriptions}")
    print(f"⏭️  Skipped (already exist): {skipped_transcriptions}")
    print(f"❌ Failed transcriptions: {failed_transcriptions}")
    print(f"📁 Audio folder: {audio_folder}")
    print(f"📁 Transcript folder: {transcript_folder}")
    
    if failed_transcriptions > 0:
        print(f"\n⚠️  {failed_transcriptions} transcriptions failed. Check the audio files and try again.")
    
    if successful_transcriptions > 0:
        print(f"\n🎉 Successfully transcribed {successful_transcriptions} audio files!")
        print(f"📝 Transcripts use {CHUNK_SECONDS}-second chunks with {PADDING_SECONDS}-second padding")
        print("💡 To generate AI titles for chunks, run: python generate_chunk_titles.py")

    input("\nPress Enter to exit...")


if __name__ == "__main__":
    main()
